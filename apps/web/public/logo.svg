<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   version="1.1"
   id="svg1"
   width="600.04932"
   height="272.76532"
   viewBox="0 0 600.04932 272.76532"
   sodipodi:docname="IBC_Logotype_RGB_Full_Color.ai"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs
     id="defs1">
    <color-profile
       inkscape:label="sRGB IEC61966-2.1"
       name="sRGB IEC61966-2.1"
       xlink:href="data:application/vnd.iccprofile;base64,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"
       id="color-profile1" />
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath2">
      <path
         d="M 0,204.574 H 450.037 V 0 H 0 Z"
         transform="translate(-232.0213,-92.842205)"
         id="path2" />
    </clipPath>
  </defs>
  <sodipodi:namedview
     id="namedview1"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1">
    <inkscape:page
       x="0"
       y="0"
       inkscape:label="1"
       id="page1"
       width="600.04932"
       height="272.76532"
       margin="73.458397 161.60001 73.458664 161.59866"
       bleed="0" />
  </sodipodi:namedview>
  <g
     id="layer-MC0"
     inkscape:groupmode="layer"
     inkscape:label="Layer 1">
    <path
       id="path1"
       d="m 0,0 h -13.819 c -1.345,0 -2.436,1.09 -2.436,2.436 v 14.001 c 0,1.345 1.091,2.436 2.436,2.436 H 0 c 1.345,0 2.436,-1.091 2.436,-2.436 V 2.436 C 2.436,1.09 1.345,0 0,0 m -18.69,18.873 h -33.065 c -1.345,0 -2.435,1.09 -2.435,2.435 v 14.011 c 0,1.345 1.09,2.436 2.435,2.436 h 33.065 c 1.345,0 2.435,-1.091 2.435,-2.436 V 21.308 c 0,-1.345 -1.09,-2.435 -2.435,-2.435 m 2.435,-21.309 v -14.187 c 0,-1.345 -1.09,-2.436 -2.435,-2.436 h -33.07 c -1.345,0 -2.435,1.091 -2.435,2.436 v 14.187 c 0,1.346 1.09,2.436 2.435,2.436 h 33.07 c 1.345,0 2.435,-1.09 2.435,-2.436 m 96.623,21.309 h 14.013 c 1.345,0 2.435,1.09 2.435,2.435 v 14.011 c 0,1.345 -1.09,2.436 -2.435,2.436 H 80.368 c -1.345,0 -2.435,1.09 -2.435,2.435 v 14.012 c 0,1.346 -1.091,2.436 -2.436,2.436 H 40.226 c -0.019,0 0.02,0 0,0 L 22.027,38.468 C 21.569,38.011 21.312,37.391 21.312,36.745 V 21.308 c 0,-1.345 -1.091,-2.435 -2.436,-2.435 H 4.871 c -1.345,0 -2.435,1.09 -2.435,2.435 v 15.445 c 0,0.642 -0.254,1.258 -0.705,1.714 L -15.54,55.916 c -0.457,0.462 -1.081,0.722 -1.731,0.722 h -34.484 c -1.345,0 -2.435,-1.09 -2.435,-2.436 V 40.19 c 0,-1.345 -1.091,-2.435 -2.436,-2.435 h -14.011 c -1.345,0 -2.435,1.09 -2.435,2.435 v 14.012 c 0,1.346 -1.091,2.436 -2.436,2.436 h -14.004 c -1.345,0 -2.435,-1.09 -2.435,-2.436 V 40.19 c 0,-1.345 -1.091,-2.435 -2.436,-2.435 h -14.004 c -1.345,0 -2.435,-1.091 -2.435,-2.436 v -70.632 c 0,-1.345 1.09,-2.435 2.435,-2.435 h 14.004 c 1.345,0 2.436,1.09 2.436,2.435 v 70.632 c 0,1.345 1.09,2.436 2.435,2.436 h 14.004 c 1.345,0 2.436,-1.091 2.436,-2.436 v -70.632 c 0,-1.345 1.09,-2.435 2.435,-2.435 h 53.374 c 0.645,0 1.265,0.256 1.722,0.713 l 17.263,17.263 c 0.457,0.457 0.714,1.076 0.714,1.722 V -2.436 C 2.436,-1.09 3.526,0 4.871,0 h 14.005 c 1.345,0 2.436,-1.09 2.436,-2.436 v -15.605 c 0,-0.651 0.26,-1.275 0.723,-1.733 l 17.48,-17.271 c 0.456,-0.451 1.071,-0.703 1.711,-0.703 h 53.155 c 1.345,0 2.435,1.09 2.435,2.435 v 13.819 c 0,1.345 -1.09,2.435 -2.435,2.435 H 42.662 c -1.345,0 -2.436,1.091 -2.436,2.436 V -2.436 C 40.226,-1.09 39.136,0 37.791,0 H 23.747 c -1.345,0 -2.435,1.09 -2.435,2.436 v 14.001 c 0,1.345 1.09,2.436 2.435,2.436 h 14.044 c 1.345,0 2.435,1.09 2.435,2.435 v 14.011 c 0,1.345 1.091,2.436 2.436,2.436 h 32.835 c 1.345,0 2.436,-1.091 2.436,-2.436 V 21.308 c 0,-1.345 1.09,-2.435 2.435,-2.435"
       style="fill:#00c729;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,309.36173,148.97573)"
       clip-path="url(#clipPath2)" />
  </g>
</svg>
