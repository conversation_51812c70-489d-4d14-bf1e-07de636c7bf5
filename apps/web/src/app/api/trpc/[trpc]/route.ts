import { fetchRe<PERSON><PERSON>and<PERSON> } from '@trpc/server/adapters/fetch';
import { appRouter } from '@/lib/server/routers';
import { createContext } from '@/lib/server/context';
import { NextRequest } from 'next/server';

function handler(req: NextRequest) {
  console.log("🔍 [TRPC ROUTE] ==================== TRPC REQUEST ====================");
  console.log("🔍 [TRPC ROUTE] URL:", req.url);
  console.log("🔍 [TRPC ROUTE] Method:", req.method);
  console.log("🔍 [TRPC ROUTE] User-Agent:", req.headers.get('user-agent'));
  console.log("🔍 [TRPC ROUTE] Referer:", req.headers.get('referer'));

  return fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => {
      console.log("🔍 [TRPC ROUTE] Creating context for request...");
      return createContext(req);
    },
    onError: ({ error, path, input }) => {
      console.error("❌ [TRPC ROUTE] Error:", {
        path,
        input,
        error: error.message,
        code: error.code,
        cause: error.cause
      });
    }
  });
}

export { handler as GET, handler as POST };