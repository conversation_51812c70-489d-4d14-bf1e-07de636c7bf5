"use client";

import { useState, useEffect, useCallback, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { AuthDebugPanel } from "@/components/debug/auth-debug";
import { SearchBar } from "@/components/search-bar";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/loader";
import { toast } from "sonner";
import { Plus, Edit, Trash2, Shield, BarChart3, ChevronDown, X, ExternalLink, Settings, Database, Tag, User, Type, Hash, Calendar, ChevronLeft, ChevronRight } from "lucide-react";
import { z } from "zod";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

// Database enum constants
const CONTENT_TYPES = ["twitter", "presskit", "marketing", "incubation", "testimonials"] as const;
const TWITTER_CONTENT_TYPES = ["space", "interview", "tweet", "thread", "retweet"] as const;
const CONTENT_CATEGORIES = [
  "ai", "defi", "gaming", "memecoin", "web2", "crypto", "politics", 
  "news", "markets", "nft", "metaverse", "blockchain", "bitcoin", 
  "ethereum", "solana", "regulation", "technology", "finance"
] as const;

// Form validation schema matching tRPC backend
const contentFormSchema = z.object({
  content_link: z.string().url("Please enter a valid URL"),
  content_tags: z.array(z.string()).default([]),
  host: z.string().min(1, "Host account is required").transform(val => val.trim().toLowerCase()),
  content_account: z.array(z.string()).min(1, "At least one content account is required")
    .transform(accounts => accounts.filter(account => account.trim() !== '')),
  content_created_date: z.string().min(1, "Created date is required"),
  content_types: z.array(z.enum(CONTENT_TYPES)).default([]),
  twitter_content_type: z.enum(TWITTER_CONTENT_TYPES).nullable().optional(),
  content_views: z.number().min(0, "Views must be 0 or greater").default(0),
  content_listeners: z.number().min(0, "Listeners must be 0 or greater").default(0),
  twitter_impressions: z.number().min(0, "Impressions must be 0 or greater").default(0),
  content_follow_increase: z.number().min(0, "Follow increase must be 0 or greater").default(0),
  content_title: z.string().nullable().optional(),
  content_description: z.string().nullable().optional(),
  content_categories: z.array(z.enum(CONTENT_CATEGORIES)).default([]),
});

type FormErrors = Partial<Record<keyof ContentFormData, string>>;

// Error display component
interface FieldErrorProps {
  error?: string;
}

const FieldError = ({ error }: FieldErrorProps) => {
  if (!error) return null;
  return <p className="text-sm text-destructive mt-1">{error}</p>;
};

// Multi-select dropdown component
interface MultiSelectDropdownProps {
  label: string;
  value: string[];
  options: readonly string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  onAddNew?: (newValue: string) => Promise<void>;
  addNewLabel?: string;
  canAddNew?: boolean;
}

const MultiSelectDropdown = ({ label, value, options, onChange, placeholder, onAddNew, addNewLabel, canAddNew = false }: MultiSelectDropdownProps) => {
  const [isAddingNew, setIsAddingNew] = useState(false);

  const toggleOption = (option: string) => {
    if (value.includes(option)) {
      onChange(value.filter(item => item !== option));
    } else {
      onChange([...value, option]);
    }
  };

  const removeOption = (option: string) => {
    onChange(value.filter(item => item !== option));
  };

  const handleAddNew = async () => {
    if (!onAddNew) return;
    
    const newValue = prompt(`Add new ${addNewLabel || 'option'}:`);
    if (newValue && newValue.trim()) {
      setIsAddingNew(true);
      try {
        await onAddNew(newValue.trim());
        // Automatically select the newly added option
        if (!value.includes(newValue.trim().toLowerCase())) {
          onChange([...value, newValue.trim().toLowerCase()]);
        }
        toast.success(`${addNewLabel || 'Option'} "${newValue.trim()}" added successfully!`);
      } catch (error) {
        console.error('Error adding new option:', error);
        toast.error(`Failed to add ${addNewLabel || 'option'}`);
      } finally {
        setIsAddingNew(false);
      }
    }
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between h-auto min-h-10 px-3 py-2 max-h-32"
          >
            <div className="flex flex-wrap gap-1 max-h-24 overflow-y-auto flex-1 pr-2">
              {value.length === 0 ? (
                <span className="text-muted-foreground text-sm">{placeholder || "Select options..."}</span>
              ) : value.length <= 3 ? (
                value.map(item => (
                  <Badge 
                    key={item} 
                    variant="secondary" 
                    className="text-xs cursor-pointer hover:bg-secondary/80"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeOption(item);
                    }}
                  >
                    {item}
                    <X className="ml-1 h-3 w-3" />
                  </Badge>
                ))
              ) : (
                <>
                  {value.slice(0, 2).map(item => (
                    <Badge 
                      key={item} 
                      variant="secondary" 
                      className="text-xs cursor-pointer hover:bg-secondary/80"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeOption(item);
                      }}
                    >
                      {item}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  ))}
                  <Badge variant="outline" className="text-xs">
                    +{value.length - 2} more
                  </Badge>
                </>
              )}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50 flex-shrink-0" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64 max-h-64 overflow-y-auto">
          <div className="p-2">
            <div className="text-xs text-muted-foreground mb-2">
              {value.length > 0 ? `${value.length} selected` : "Select options"}
            </div>
          </div>
          {canAddNew && onAddNew && (
            <>
              <DropdownMenuItem 
                onClick={handleAddNew}
                className="cursor-pointer px-3 py-2 bg-blue-50 hover:bg-blue-100 dark:bg-blue-950/50 dark:hover:bg-blue-900/50 border-b"
                disabled={isAddingNew}
              >
                <div className="flex items-center space-x-2 w-full">
                  <Plus className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">
                    {isAddingNew ? 'Adding...' : `Add New ${addNewLabel || 'Option'}`}
                  </span>
                </div>
              </DropdownMenuItem>
              <div className="h-px bg-border my-1" />
            </>
          )}
          {options.map(option => (
            <DropdownMenuItem 
              key={option}
              onClick={() => toggleOption(option)}
              className="cursor-pointer px-3 py-2"
            >
              <div className="flex items-center space-x-2 w-full">
                <div className={`w-4 h-4 border rounded flex-shrink-0 ${value.includes(option) ? 'bg-primary border-primary' : 'border-input'}`}>
                  {value.includes(option) && <div className="w-full h-full bg-primary-foreground rounded-sm m-0.5" />}
                </div>
                <span className="capitalize text-sm flex-1">{option}</span>
                {value.includes(option) && (
                  <X className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                )}
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

// Enhanced date picker component
interface DatePickerProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
  error?: string;
}

const DatePicker = ({ label, value, onChange, required = false, error }: DatePickerProps) => {
  const today = new Date().toISOString().split('T')[0];
  const maxDate = today; // Don't allow future dates for content creation
  const minDate = '2020-01-01'; // Reasonable minimum date

  return (
    <div className="space-y-2">
      <Label htmlFor="date-picker" className="flex items-center gap-2">
        <Calendar className="h-4 w-4" />
        {label}
        {required && <span className="text-destructive">*</span>}
      </Label>
      <div className="relative">
        <Input
          id="date-picker"
          type="date"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          required={required}
          min={minDate}
          max={maxDate}
          className={`${error ? "border-destructive" : ""} pr-10`}
        />
        <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
      </div>
      {error && <p className="text-sm text-destructive">{error}</p>}
      <p className="text-xs text-muted-foreground">
        Select the date when this content was originally created
      </p>
    </div>
  );
};

// Single select dropdown component
interface SingleSelectDropdownProps {
  label: string;
  value: string | null;
  options: readonly string[];
  onChange: (value: string | null) => void;
  placeholder?: string;
  allowNull?: boolean;
}

const SingleSelectDropdown = ({ label, value, options, onChange, placeholder, allowNull = true }: SingleSelectDropdownProps) => {
  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <span className={value ? "" : "text-muted-foreground"}>
              {value ? value.charAt(0).toUpperCase() + value.slice(1) : (placeholder || "Select option...")}
            </span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          {allowNull && (
            <DropdownMenuItem onClick={() => onChange(null)}>
              <span className="text-muted-foreground">None selected</span>
            </DropdownMenuItem>
          )}
          {options.map(option => (
            <DropdownMenuItem 
              key={option}
              onClick={() => onChange(option)}
              className="cursor-pointer"
            >
              <span className="capitalize">{option}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

// Metric input component with k/m/b notation support
interface MetricInputProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  placeholder?: string;
  id?: string;
  error?: string;
}

const MetricInput = ({ label, value, onChange, placeholder, id, error }: MetricInputProps) => {
  const [displayValue, setDisplayValue] = useState(value.toString());

  // Update display value when prop value changes (for editing)
  useEffect(() => {
    setDisplayValue(value.toString());
  }, [value]);

  // Parse various number formats to actual numbers
  const parseMetricValue = (input: string): number => {
    if (!input || input.trim() === '') return 0;
    
    // Remove any whitespace and convert to lowercase
    const cleaned = input.trim().toLowerCase().replace(/,/g, '');
    
    // Handle k/m/b notation
    const multipliers: { [key: string]: number } = {
      'k': 1000,
      'm': 1000000,
      'b': 1000000000
    };
    
    // Check if the string ends with a multiplier
    const lastChar = cleaned.slice(-1);
    if (multipliers[lastChar]) {
      const numPart = cleaned.slice(0, -1);
      const baseNumber = parseFloat(numPart);
      if (!isNaN(baseNumber)) {
        return Math.floor(baseNumber * multipliers[lastChar]);
      }
    }
    
    // Handle regular numbers (including decimals, but floor them)
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : Math.floor(Math.max(0, parsed));
  };

  // Format number for display (reverse of parsing)
  const formatNumber = (num: number): string => {
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(num % 1000000000 === 0 ? 0 : 1) + 'b';
    } else if (num >= 1000000) {
      return (num / 1000000).toFixed(num % 1000000 === 0 ? 0 : 1) + 'm';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(num % 1000 === 0 ? 0 : 1) + 'k';
    }
    return num.toString();
  };

  const handleInputChange = (input: string) => {
    setDisplayValue(input);
  };

  const handleBlur = () => {
    const parsedValue = parseMetricValue(displayValue);
    onChange(parsedValue);
    // Update display to show the clean parsed value
    setDisplayValue(parsedValue.toString());
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle Enter key
    if (e.key === 'Enter') {
      handleBlur();
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={id} className="flex items-center gap-2">
        {label}
      </Label>
      <div className="relative">
        <Input
          id={id}
          type="text"
          value={displayValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder || "0, 1k, 1.5m, 2b"}
          className={`${error ? "border-destructive" : ""}`}
        />
      </div>
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>Examples: 1k, 1.5m, 2b, 1,234,567</span>
        {value > 0 && (
          <span className="font-medium">
            = {value.toLocaleString()}
          </span>
        )}
      </div>
      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
};

interface ContentFormData {
  content_link: string;
  content_tags: string[];
  host: string;
  content_account: string[];
  content_created_date: string;
  content_types: string[];
  twitter_content_type: string | null;
  content_views: number;
  content_listeners: number;
  twitter_impressions: number;
  content_follow_increase: number;
  content_title: string | null;
  content_description: string | null;
  content_categories: string[];
}

const initialFormData: ContentFormData = {
  content_link: "",
  content_tags: [],
  host: "",
  content_account: [""],
  content_created_date: new Date().toISOString().split('T')[0],
  content_types: [],
  twitter_content_type: null,
  content_views: 0,
  content_listeners: 0,
  twitter_impressions: 0,
  content_follow_increase: 0,
  content_title: null,
  content_description: null,
  content_categories: [],
};

interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

// Helper function to format dates
const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch {
    return dateString;
  }
};

// Pagination component
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
}

const Pagination = ({ currentPage, totalPages, onPageChange, totalItems, itemsPerPage }: PaginationProps) => {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  if (totalPages <= 1) return null;

  return (
    <div className="flex items-center justify-between px-2 py-4">
      <div className="text-sm text-muted-foreground">
        Showing {startItem}-{endItem} of {totalItems} entries
      </div>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
        
        <div className="flex items-center space-x-1">
          {getVisiblePages().map((page, index) => (
            <Button
              key={index}
              variant={page === currentPage ? "default" : "outline"}
              size="sm"
              className="w-10 h-10"
              onClick={() => typeof page === 'number' && onPageChange(page)}
              disabled={typeof page !== 'number'}
            >
              {page}
            </Button>
          ))}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

function AdminPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState<ContentFormData>(initialFormData);
  const [showForm, setShowForm] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [activeTab, setActiveTab] = useState<'content' | 'metadata'>('content');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Items per page
  const [editingContent, setEditingContent] = useState<(ContentFormData & { id: number }) | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  if (process.env.NODE_ENV === 'development') {
    console.log('Admin page loaded');
  }

  // Initialize search state from URL parameters
  const [searchQuery, setSearchQuery] = useState(() => searchParams.get("q") || "");
  const [filters, setFilters] = useState<Filters>(() => ({
    contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || [],
    categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
    sortBy: searchParams.get("sort") || "impressions",
    sortOrder: searchParams.get("order") || "desc"
  }));

  // Track if search is active
  const isSearchActive = searchQuery.length > 0 || filters.contentTypes.length > 0 || filters.categories.length > 0;

  // Update URL when search/filters change
  const updateURL = useCallback(() => {
    const params = new URLSearchParams();
    if (searchQuery) params.set("q", searchQuery);
    if (filters.contentTypes.length > 0) params.set("types", filters.contentTypes.join(","));
    if (filters.categories.length > 0) params.set("categories", filters.categories.join(","));
    if (filters.sortBy !== "impressions") params.set("sort", filters.sortBy);
    if (filters.sortOrder !== "desc") params.set("order", filters.sortOrder);

    const newURL = params.toString() ? `?${params.toString()}` : window.location.pathname;
    window.history.replaceState({}, "", newURL);
  }, [searchQuery, filters]);

  useEffect(() => {
    updateURL();
  }, [updateURL]);

  // Use the same authentication method as the header for consistency
  const { data: user, isLoading: userLoading } = trpc.getCurrentUser.useQuery(undefined, {
    retry: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Check if user is admin - now safe for unauthenticated users
  const userProfileQuery = trpc.getUserProfile.useQuery(undefined, {
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Comprehensive debugging (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log("🔍 [ADMIN PAGE] ==================== ADMIN PAGE STATE ====================");
    console.log("🔍 [ADMIN PAGE] getCurrentUser query:", {
      hasUser: !!user,
      userEmail: user?.email,
      userId: user?.id,
      userLoading,
      userError: userLoading ? 'Loading...' : (!user ? 'No user' : 'User found')
    });

    console.log("🔍 [ADMIN PAGE] getUserProfile query:", {
      profileLoading: userProfileQuery.isLoading,
      hasProfileData: !!userProfileQuery.data,
      profileData: userProfileQuery.data,
      profileError: userProfileQuery.error?.message,
      profileErrorCode: userProfileQuery.error?.data?.code,
      userRole: userProfileQuery.data?.role,
      queryStatus: userProfileQuery.status,
      queryFetchStatus: userProfileQuery.fetchStatus
    });

    // Additional debugging for profile data
    if (userProfileQuery.data) {
      console.log("✅ [ADMIN PAGE] Profile data found:", userProfileQuery.data);
    } else if (userProfileQuery.error) {
      console.log("❌ [ADMIN PAGE] Profile query error details:", {
        message: userProfileQuery.error.message,
        data: userProfileQuery.error.data,
        shape: userProfileQuery.error.shape
      });
    } else if (!userProfileQuery.isLoading) {
      console.log("⚠️ [ADMIN PAGE] Profile query returned null (no error, no data)");
    }

    // Debug localStorage/sessionStorage
    if (typeof window !== 'undefined') {
      console.log("🔍 [ADMIN PAGE] Browser storage:", {
        localStorage: Object.keys(localStorage).filter(k => k.includes('supabase')),
        sessionStorage: Object.keys(sessionStorage).filter(k => k.includes('supabase')),
        cookies: document.cookie.split(';').filter(c => c.includes('sb-')).map(c => c.trim().split('=')[0])
      });
    }
  }

  // Get content for admin management
  const contentQuery = trpc.getAllContent.useQuery({
    page: currentPage,
    limit: pageSize,
  });

  // Get filtered content for search functionality
  const filteredContentQuery = trpc.getFilteredContent.useQuery({
    search: searchQuery,
    contentTypes: filters.contentTypes,
    categories: filters.categories,
    sortBy: filters.sortBy as "impressions" | "date" | "likes" | "retweets",
    sortOrder: filters.sortOrder as "desc" | "asc",
    limit: 50
  });

  // Get metadata for management (only fetch when metadata tab is active)
  const metadataQuery = trpc.getFilterMetadata.useQuery({}, {
    enabled: activeTab === 'metadata'
  });

  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 [ADMIN] Search state:', {
      searchQuery,
      filters,
      isSearchActive,
      filteredResults: filteredContentQuery.data?.length,
      allContent: contentQuery.data?.data?.length
    });
  }

  // Search handlers
  const handleSearch = (query: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [ADMIN] Search query:', query);
    }
    setSearchQuery(query);
  };

  const handleFilter = (filterType: string, values: string[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [ADMIN] Filter change:', filterType, values);
    }
    setFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };

  const handleSort = (sortBy: string, sortOrder?: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [ADMIN] Sort change:', sortBy, sortOrder);
    }
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || prev.sortOrder
    }));
  };

  // Mutations
  const createContentMutation = trpc.createContent.useMutation({
    onSuccess: () => {
      toast.success("Content created successfully!");
      setFormData(initialFormData);
      setShowForm(false);
      setCurrentPage(1); // Reset to first page
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to create content: ${error.message}`);
    },
  });

  const deleteContentMutation = trpc.deleteContent.useMutation({
    onSuccess: () => {
      toast.success("Content deleted successfully!");
      // Check if we need to go back a page (if current page becomes empty)
      const currentData = contentQuery.data;
      if (currentData && currentData.data.length === 1 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete content: ${error.message}`);
    },
  });

  const updateContentMutation = trpc.updateContent.useMutation({
    onSuccess: () => {
      toast.success("Content updated successfully!");
      setEditingContent(null);
      setIsEditing(false);
      setFormData(initialFormData);
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to update content: ${error.message}`);
    },
  });

  const updateStatsMutation = trpc.updateStats.useMutation({
    onSuccess: (data) => {
      toast.success(`Stats updated successfully! ${data.stats.totalImpressions} impressions, ${data.stats.totalContent} content pieces`);
    },
    onError: (error) => {
      toast.error(`Failed to update stats: ${error.message}`);
    },
  });

  // Metadata management mutations
  const renameFilterMutation = trpc.renameFilterValue.useMutation({
    onSuccess: (data) => {
      toast.success(`Successfully renamed value. ${data.updatedCount} content pieces updated.`);
      metadataQuery.refetch();
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to rename value: ${error.message}`);
    },
  });

  const mergeFilterMutation = trpc.mergeFilterValues.useMutation({
    onSuccess: (data) => {
      toast.success(`Successfully merged values. ${data.updatedCount} content pieces updated.`);
      metadataQuery.refetch();
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to merge values: ${error.message}`);
    },
  });

  const deleteFilterMutation = trpc.deleteFilterValue.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete value: ${error.message}`);
    },
  });

  // Add content type mutation
  const addContentTypeMutation = trpc.addContentType.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to add content type: ${error.message}`);
    },
  });

  // Add content category mutation
  const addContentCategoryMutation = trpc.addContentCategory.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to add content category: ${error.message}`);
    },
  });

  // Delete content type mutation
  const deleteContentTypeMutation = trpc.deleteContentType.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete content type: ${error.message}`);
    },
  });

  // Delete content category mutation
  const deleteContentCategoryMutation = trpc.deleteContentCategory.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete content category: ${error.message}`);
    },
  });

  // Remove content tag mutation
  const removeContentTagMutation = trpc.removeContentTag.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to remove tag: ${error.message}`);
    },
  });

  // Add Twitter content type mutation
  const addTwitterContentTypeMutation = trpc.addTwitterContentType.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to add Twitter content type: ${error.message}`);
    },
  });

  // Delete Twitter content type mutation
  const deleteTwitterContentTypeMutation = trpc.deleteTwitterContentType.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete Twitter content type: ${error.message}`);
    },
  });

  // Loading states
  if (userLoading || userProfileQuery.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  // Not authenticated - show login prompt
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 max-w-md text-center">
          <Shield className="mx-auto mb-4 h-12 w-12 text-blue-500" />
          <h1 className="text-2xl font-bold mb-2">Authentication Required</h1>
          <p className="text-muted-foreground mb-4">
            You need to sign in to access the admin panel.
          </p>
          <Button onClick={() => router.push("/")} variant="outline">
            Go to Login
          </Button>
        </Card>
      </div>
    );
  }

  // Not admin
  if (!userProfileQuery.data || userProfileQuery.data.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 max-w-md text-center">
          <Shield className="mx-auto mb-4 h-12 w-12 text-red-500" />
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don't have permission to access this page. Admin role required.
          </p>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              Current role: {userProfileQuery.data?.role || 'none'}
            </p>
            <Button onClick={() => router.push("/")} variant="outline">
              Go Home
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Helper function to extract host from Twitter/X URLs
  const extractHostFromUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      // Check if it's a Twitter/X URL
      if (hostname === 'twitter.com' || hostname === 'x.com' || hostname === 'www.twitter.com' || hostname === 'www.x.com') {
        const pathParts = urlObj.pathname.split('/').filter(Boolean);
        // First part of path should be the username
        if (pathParts.length > 0 && pathParts[0] !== 'i') {
          return pathParts[0].toLowerCase();
        }
      }
      
      // For other URLs, try to extract from domain or path
      if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
        // For YouTube, try to extract channel name from URL
        const pathParts = urlObj.pathname.split('/').filter(Boolean);
        if (pathParts.includes('c') || pathParts.includes('channel')) {
          const channelIndex = pathParts.findIndex(part => part === 'c' || part === 'channel');
          if (channelIndex >= 0 && pathParts[channelIndex + 1]) {
            return pathParts[channelIndex + 1].toLowerCase();
          }
        }
        // For @username format
        if (pathParts.length > 0 && pathParts[0].startsWith('@')) {
          return pathParts[0].substring(1).toLowerCase();
        }
      }
      
      return '';
    } catch {
      return '';
    }
  };

  const handleInputChange = (field: keyof ContentFormData, value: any) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Auto-fill host when content_link is changed
      if (field === 'content_link' && typeof value === 'string' && value.trim()) {
        const extractedHost = extractHostFromUrl(value.trim());
        if (extractedHost && (!prev.host || prev.host === '')) {
          newData.host = extractedHost;
        }
      }

      return newData;
    });
  };

  const handleArrayInputChange = (field: keyof ContentFormData, value: string) => {
    const array = value.split(',').map(item => item.trim()).filter(Boolean);
    setFormData(prev => ({
      ...prev,
      [field]: array
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous errors
    setFormErrors({});
    
    // Validate form data
    const result = contentFormSchema.safeParse(formData);
    
    if (!result.success) {
      // Extract and set validation errors
      const errors: FormErrors = {};
      result.error.issues.forEach((issue) => {
        const fieldName = issue.path[0] as keyof ContentFormData;
        errors[fieldName] = issue.message;
      });
      setFormErrors(errors);
      toast.error("Please fix the validation errors before submitting.");
      return;
    }
    
    // Submit validated data (schema already sanitized it)
    if (isEditing && editingContent) {
      updateContentMutation.mutate({
        id: editingContent.id,
        ...result.data
      });
    } else {
      createContentMutation.mutate(result.data);
    }
  };

  const handleEdit = (content: any) => {
    const editData = {
      id: content.id,
      content_link: content.content_link || "",
      content_tags: content.content_tags || [],
      host: content.host || "",
      content_account: Array.isArray(content.content_account) ? content.content_account : [content.content_account || ""],
      content_created_date: content.content_created_date ? new Date(content.content_created_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      content_types: content.content_types || [],
      twitter_content_type: content.twitter_content_type || null,
      content_views: content.content_views || 0,
      content_listeners: content.content_listeners || 0,
      twitter_impressions: content.twitter_impressions || 0,
      content_follow_increase: content.content_follow_increase || 0,
      content_title: content.content_title || null,
      content_description: content.content_description || null,
      content_categories: content.content_categories || [],
    };
    
    setEditingContent(editData);
    setFormData(editData);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleCancelEdit = () => {
    setEditingContent(null);
    setIsEditing(false);
    setFormData(initialFormData);
    setShowForm(false);
    setFormErrors({});
  };

  // Wrapper functions for inline metadata creation
  const handleAddContentType = async (value: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      addContentTypeMutation.mutate(
        { value },
        {
          onSuccess: () => {
            // Refresh the metadata to get updated enum values
            metadataQuery.refetch();
            resolve();
          },
          onError: (error) => {
            reject(error);
          }
        }
      );
    });
  };

  const handleAddContentCategory = async (value: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      addContentCategoryMutation.mutate(
        { value },
        {
          onSuccess: () => {
            // Refresh the metadata to get updated enum values
            metadataQuery.refetch();
            resolve();
          },
          onError: (error) => {
            reject(error);
          }
        }
      );
    });
  };

  const handleDelete = (id: number) => {
    if (window.confirm("Are you sure you want to delete this content?")) {
      deleteContentMutation.mutate({ id });
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <AuthDebugPanel />
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {userProfileQuery.data?.full_name || userProfileQuery.data?.email}
            </p>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={() => updateStatsMutation.mutate()}
              variant="outline"
              className="flex items-center gap-2"
              disabled={updateStatsMutation.isPending}
            >
              <BarChart3 className="h-4 w-4" />
              {updateStatsMutation.isPending ? "Updating..." : "Update Stats"}
            </Button>
            {activeTab === 'content' && (
              isEditing ? (
                <Button 
                  onClick={handleCancelEdit}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel Edit
                </Button>
              ) : (
                <Button 
                  onClick={() => setShowForm(!showForm)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Content
                </Button>
              )
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('content')}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'content'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Content Management
                </div>
              </button>
              <button
                onClick={() => setActiveTab('metadata')}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'metadata'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Metadata Management
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* Content Tab */}
        {activeTab === 'content' && (
          <>
            {/* Search Bar */}
            <div className="mb-8">
              <SearchBar
                onSearch={handleSearch}
                onFilter={handleFilter}
                onSort={handleSort}
                placeholder="Search admin content..."
                filters={filters}
                searchQuery={searchQuery}
              />
            </div>

            {/* Create Content Form */}
            {showForm && (
          <Card className="p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">
              {isEditing ? "Edit Content" : "Create New Content"}
            </h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information Section */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Basic Information</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="content_link">Content Link</Label>
                  <Input
                    id="content_link"
                    type="url"
                    value={formData.content_link}
                    onChange={(e) => handleInputChange('content_link', e.target.value)}
                    required
                    className={formErrors.content_link ? "border-destructive" : ""}
                  />
                  <FieldError error={formErrors.content_link} />
                </div>
                
                <div>
                  <Label htmlFor="host">Host Account</Label>
                  <Input
                    id="host"
                    value={formData.host}
                    onChange={(e) => handleInputChange('host', e.target.value)}
                    placeholder="Primary Twitter account (without @)"
                    required
                    className={formErrors.host ? "border-destructive" : ""}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Auto-filled from Twitter/X links, but you can edit it manually
                  </p>
                  <FieldError error={formErrors.host} />
                </div>
                
                <div>
                  <Label htmlFor="content_account">Content Accounts</Label>
                  <div className="space-y-2">
                    {formData.content_account.map((account, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={account}
                          onChange={(e) => {
                            const newAccounts = [...formData.content_account];
                            newAccounts[index] = e.target.value;
                            handleInputChange('content_account', newAccounts);
                          }}
                          placeholder="Twitter account (without @)"
                          className="flex-1"
                        />
                        {formData.content_account.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newAccounts = formData.content_account.filter((_, i) => i !== index);
                              handleInputChange('content_account', newAccounts);
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        handleInputChange('content_account', [...formData.content_account, '']);
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Account
                    </Button>
                  </div>
                  <FieldError error={formErrors.content_account} />
                </div>
                
                <div>
                  <DatePicker
                    label="Created Date"
                    value={formData.content_created_date}
                    onChange={(value) => handleInputChange('content_created_date', value)}
                    required={true}
                    error={formErrors.content_created_date}
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_title">Title (optional)</Label>
                  <Input
                    id="content_title"
                    value={formData.content_title || ''}
                    onChange={(e) => handleInputChange('content_title', e.target.value || null)}
                  />
                </div>
                
                <div>
                  <SingleSelectDropdown
                    label="Twitter Content Type"
                    value={formData.twitter_content_type}
                    options={TWITTER_CONTENT_TYPES}
                    onChange={(value) => handleInputChange('twitter_content_type', value)}
                    placeholder="Select content type..."
                    allowNull={true}
                  />
                </div>
                </div>
              </div>
              
              {/* Content Metrics Section */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Content Metrics</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <MetricInput
                    id="content_views"
                    label="Views"
                    value={formData.content_views}
                    onChange={(value) => handleInputChange('content_views', value)}
                    placeholder="Video views (e.g., 1.2m)"
                  />
                  <MetricInput
                    id="content_listeners"
                    label="Listeners"
                    value={formData.content_listeners}
                    onChange={(value) => handleInputChange('content_listeners', value)}
                    placeholder="Audio listeners (e.g., 500k)"
                  />
                  <MetricInput
                    id="twitter_impressions"
                    label="Impressions"
                    value={formData.twitter_impressions}
                    onChange={(value) => handleInputChange('twitter_impressions', value)}
                    placeholder="Twitter impressions (e.g., 2.5m)"
                  />
                  <MetricInput
                    id="content_follow_increase"
                    label="Follow Increase"
                    value={formData.content_follow_increase}
                    onChange={(value) => handleInputChange('content_follow_increase', value)}
                    placeholder="New followers (e.g., 1.2k)"
                  />
                </div>
              </div>

              {/* Content Details Section */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Content Details</h3>
                <div>
                  <Label htmlFor="content_description">Description (optional)</Label>
                  <Input
                    id="content_description"
                    value={formData.content_description || ''}
                    onChange={(e) => handleInputChange('content_description', e.target.value || null)}
                    placeholder="Brief description of the content..."
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_tags">Tags (comma-separated)</Label>
                  <Input
                    id="content_tags"
                    value={formData.content_tags.join(', ')}
                    onChange={(e) => handleArrayInputChange('content_tags', e.target.value)}
                    placeholder="tag1, tag2, tag3"
                  />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <MultiSelectDropdown
                      label="Content Types"
                      value={formData.content_types}
                      options={CONTENT_TYPES}
                      onChange={(value) => handleInputChange('content_types', value)}
                      placeholder="Select content types..."
                      canAddNew={true}
                      onAddNew={handleAddContentType}
                      addNewLabel="Content Type"
                    />
                  </div>
                  
                  <div>
                    <MultiSelectDropdown
                      label="Categories"
                      value={formData.content_categories}
                      options={CONTENT_CATEGORIES}
                      onChange={(value) => handleInputChange('content_categories', value)}
                      placeholder="Select categories..."
                      canAddNew={true}
                      onAddNew={handleAddContentCategory}
                      addNewLabel="Category"
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button 
                  type="submit" 
                  disabled={createContentMutation.isPending || updateContentMutation.isPending}
                >
                  {isEditing
                    ? (updateContentMutation.isPending ? "Updating..." : "Update Content")
                    : (createContentMutation.isPending ? "Creating..." : "Create Content")
                  }
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={isEditing ? handleCancelEdit : () => setShowForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Card>
        )}

        {/* Content List */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">
            {isSearchActive ? `Search Results${searchQuery ? ` for "${searchQuery}"` : ''}` : 'Manage Content'}
          </h2>

          {(isSearchActive ? filteredContentQuery.isLoading : contentQuery.isLoading) ? (
            <div className="flex justify-center py-8">
              <Loader />
            </div>
          ) : (
            <div className="space-y-4">
              {(isSearchActive ? filteredContentQuery.data : contentQuery.data?.data)?.map((content) => (
                <div
                  key={content.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="space-y-2">
                      <h3 className="font-medium text-lg">
                        {content.content_title || content.content_account}
                      </h3>

                      {content.content_description && (
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {content.content_description}
                        </p>
                      )}

                      <div className="flex items-center gap-2">
                        <a
                          href={content.content_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800 hover:underline flex items-center gap-1 transition-colors"
                        >
                          Link
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      </div>

                      {/* Content Types and Categories */}
                      <div className="flex flex-wrap gap-1 mt-2">
                        {content.content_types?.map((type) => (
                          <Badge key={type} variant="secondary" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                        {content.twitter_content_type && (
                          <Badge variant="outline" className="text-xs">
                            {content.twitter_content_type}
                          </Badge>
                        )}
                        {content.content_categories?.slice(0, 3).map((category) => (
                          <Badge key={category} variant="outline" className="text-xs">
                            #{category}
                          </Badge>
                        ))}
                        {content.content_categories && content.content_categories.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{content.content_categories.length - 3} more
                          </Badge>
                        )}
                      </div>

                      {/* Content Metrics Table */}
                      <div className="mt-3 pt-3 border-t">
                        <div className="grid grid-cols-2 lg:grid-cols-5 gap-3 text-xs">
                          <div className="text-center">
                            <div className="font-medium text-blue-600">{content.content_views?.toLocaleString() || 0}</div>
                            <div className="text-muted-foreground">Views</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-purple-600">{content.content_listeners?.toLocaleString() || 0}</div>
                            <div className="text-muted-foreground">Listeners</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-green-600">{content.twitter_impressions?.toLocaleString() || 0}</div>
                            <div className="text-muted-foreground">Impressions</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-orange-600">{content.content_follow_increase?.toLocaleString() || 0}</div>
                            <div className="text-muted-foreground">Follow Increase</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-600">{formatDate(content.content_created_date)}</div>
                            <div className="text-muted-foreground">Created</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(content)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(content.id)}
                      disabled={deleteContentMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}

              {(isSearchActive ? filteredContentQuery.data : contentQuery.data?.data)?.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  {isSearchActive
                    ? "No content found matching your search criteria."
                    : "No content found. Create your first entry!"
                  }
                </div>
              )}
            </div>
          )}
          
          {/* Pagination for non-search results */}
          {!isSearchActive && contentQuery.data && contentQuery.data.totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={contentQuery.data.totalPages}
              onPageChange={handlePageChange}
              totalItems={contentQuery.data.total}
              itemsPerPage={pageSize}
            />
          )}
        </Card>
          </>
        )}

        {/* Metadata Tab */}
        {activeTab === 'metadata' && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-semibold">Content Metadata Management</h2>
              <p className="text-muted-foreground mt-2">
                Manage tags, accounts, types, and categories used across all content
              </p>
            </div>

            {metadataQuery.isLoading ? (
              <div className="flex justify-center py-8">
                <Loader />
              </div>
            ) : metadataQuery.error ? (
              <Card className="p-6">
                <div className="text-center text-destructive">
                  <p>Error loading metadata: {metadataQuery.error.message}</p>
                </div>
              </Card>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Content Tags */}
                {metadataQuery.data?.content_tags && (
                  <Card className="p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Tag className="h-5 w-5 text-blue-500" />
                      <h3 className="text-lg font-semibold">Content Tags</h3>
                      <Badge variant="secondary">{metadataQuery.data.content_tags.length}</Badge>
                    </div>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {metadataQuery.data.content_tags
                        .sort((a, b) => b.usageCount - a.usageCount)
                        .map((tag) => (
                          <div key={tag.value} className="flex items-center justify-between p-2 border rounded hover:bg-muted/50 transition-colors group">
                            <div className="flex items-center gap-2 flex-1">
                              <span className="text-sm font-mono">{tag.value}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {tag.usageCount} uses
                              </Badge>
                              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    const newValue = prompt(`Rename "${tag.value}" to:`, tag.value);
                                    if (newValue && newValue !== tag.value) {
                                      renameFilterMutation.mutate({
                                        type: 'content_tags',
                                        oldValue: tag.value,
                                        newValue: newValue
                                      });
                                    }
                                  }}
                                  disabled={renameFilterMutation.isPending}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                                  onClick={() => {
                                    if (tag.usageCount > 0) {
                                      const confirmed = confirm(`Remove tag "${tag.value}" from ${tag.usageCount} content pieces? This cannot be undone.`);
                                      if (!confirmed) return;
                                      removeContentTagMutation.mutate({
                                        value: tag.value,
                                        force: true
                                      });
                                    } else {
                                      const confirmed = confirm(`Delete unused tag "${tag.value}"?`);
                                      if (confirmed) {
                                        removeContentTagMutation.mutate({
                                          value: tag.value,
                                          force: false
                                        });
                                      }
                                    }
                                  }}
                                  disabled={removeContentTagMutation.isPending}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </Card>
                )}

                {/* Content Accounts */}
                {metadataQuery.data?.content_account && (
                  <Card className="p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <User className="h-5 w-5 text-green-500" />
                      <h3 className="text-lg font-semibold">Content Accounts</h3>
                      <Badge variant="secondary">{metadataQuery.data.content_account.length}</Badge>
                    </div>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {metadataQuery.data.content_account
                        .sort((a, b) => b.usageCount - a.usageCount)
                        .map((account) => (
                          <div key={account.value} className="flex items-center justify-between p-2 border rounded hover:bg-muted/50 transition-colors group">
                            <div className="flex items-center gap-2 flex-1">
                              <span className="text-sm font-medium">{account.value}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {account.usageCount} uses
                              </Badge>
                              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    const newValue = prompt(`Rename account "${account.value}" to:`, account.value);
                                    if (newValue && newValue !== account.value) {
                                      renameFilterMutation.mutate({
                                        type: 'content_account',
                                        oldValue: account.value,
                                        newValue: newValue
                                      });
                                    }
                                  }}
                                  disabled={renameFilterMutation.isPending}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    // Show merge dialog - find other accounts to merge with
                                    const otherAccounts = metadataQuery.data?.content_account
                                      ?.filter(a => a.value !== account.value)
                                      .map(a => a.value) || [];
                                    
                                    if (otherAccounts.length === 0) {
                                      toast.info("No other accounts to merge with");
                                      return;
                                    }

                                    const targetAccount = prompt(
                                      `Merge "${account.value}" into which account?\n\nAvailable accounts:\n${otherAccounts.slice(0, 10).join(', ')}${otherAccounts.length > 10 ? '...' : ''}\n\nEnter target account name:`
                                    );
                                    
                                    if (targetAccount && otherAccounts.includes(targetAccount)) {
                                      mergeFilterMutation.mutate({
                                        type: 'content_account',
                                        sourceValues: [account.value],
                                        targetValue: targetAccount
                                      });
                                    } else if (targetAccount) {
                                      toast.error("Target account not found");
                                    }
                                  }}
                                  disabled={mergeFilterMutation.isPending}
                                >
                                  <Database className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </Card>
                )}

                {/* Content Types */}
                {metadataQuery.data?.content_types && (
                  <Card className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <Type className="h-5 w-5 text-purple-500" />
                        <h3 className="text-lg font-semibold">Content Types</h3>
                        <Badge variant="secondary">{metadataQuery.data.content_types.length}</Badge>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const newType = prompt("Enter new content type:");
                          if (newType && newType.trim()) {
                            addContentTypeMutation.mutate({ value: newType.trim() });
                          }
                        }}
                        disabled={addContentTypeMutation.isPending}
                        className="flex items-center gap-1"
                      >
                        <Plus className="h-3 w-3" />
                        Add Type
                      </Button>
                    </div>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {metadataQuery.data.content_types
                        .sort((a, b) => b.usageCount - a.usageCount)
                        .map((type) => (
                          <div key={type.value} className="flex items-center justify-between p-2 border rounded hover:bg-muted/50 transition-colors group">
                            <div className="flex items-center gap-2 flex-1">
                              <span className="text-sm font-medium capitalize">{type.value}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {type.usageCount} uses
                              </Badge>
                              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    toast.info("Content types are enum values. Database schema changes required for renaming.");
                                  }}
                                  disabled={true}
                                >
                                  <Edit className="h-3 w-3 opacity-50" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                  onClick={() => {
                                    if (type.usageCount > 0) {
                                      const forceDelete = confirm(`"${type.value}" is used in ${type.usageCount} content pieces. Delete anyway? This may cause issues.`);
                                      if (forceDelete) {
                                        deleteContentTypeMutation.mutate({ value: type.value, force: true });
                                      }
                                    } else {
                                      const confirmDelete = confirm(`Delete content type "${type.value}"?`);
                                      if (confirmDelete) {
                                        deleteContentTypeMutation.mutate({ value: type.value });
                                      }
                                    }
                                  }}
                                  disabled={deleteContentTypeMutation.isPending}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </Card>
                )}

                {/* Twitter Content Types */}
                {metadataQuery.data?.twitter_content_type && (
                  <Card className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <Hash className="h-5 w-5 text-blue-400" />
                        <h3 className="text-lg font-semibold">Twitter Content Types</h3>
                        <Badge variant="secondary">{metadataQuery.data.twitter_content_type.length}</Badge>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const newType = prompt("Enter new Twitter content type:");
                          if (newType && newType.trim()) {
                            addTwitterContentTypeMutation.mutate({ value: newType.trim() });
                          }
                        }}
                        disabled={addTwitterContentTypeMutation.isPending}
                        className="flex items-center gap-1"
                      >
                        <Plus className="h-3 w-3" />
                        Add Type
                      </Button>
                    </div>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {metadataQuery.data.twitter_content_type
                        .sort((a, b) => b.usageCount - a.usageCount)
                        .map((type) => (
                          <div key={type.value} className="flex items-center justify-between p-2 border rounded hover:bg-muted/50 transition-colors group">
                            <div className="flex items-center gap-2 flex-1">
                              <span className="text-sm font-medium capitalize">{type.value}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {type.usageCount} uses
                              </Badge>
                              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    toast.info("Twitter content types are enum values. Database schema changes required for renaming.");
                                  }}
                                  disabled={true}
                                >
                                  <Edit className="h-3 w-3 opacity-50" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                  onClick={() => {
                                    if (type.usageCount > 0) {
                                      const forceDelete = confirm(`"${type.value}" is used in ${type.usageCount} content pieces. Delete anyway? This may cause issues.`);
                                      if (forceDelete) {
                                        deleteTwitterContentTypeMutation.mutate({ value: type.value, force: true });
                                      }
                                    } else {
                                      const confirmDelete = confirm(`Delete Twitter content type "${type.value}"?`);
                                      if (confirmDelete) {
                                        deleteTwitterContentTypeMutation.mutate({ value: type.value });
                                      }
                                    }
                                  }}
                                  disabled={deleteTwitterContentTypeMutation.isPending}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </Card>
                )}

                {/* Content Categories */}
                {metadataQuery.data?.content_categories && (
                  <Card className="p-6 lg:col-span-2">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <Database className="h-5 w-5 text-orange-500" />
                        <h3 className="text-lg font-semibold">Content Categories</h3>
                        <Badge variant="secondary">{metadataQuery.data.content_categories.length}</Badge>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const newCategory = prompt("Enter new content category:");
                          if (newCategory && newCategory.trim()) {
                            addContentCategoryMutation.mutate({ value: newCategory.trim() });
                          }
                        }}
                        disabled={addContentCategoryMutation.isPending}
                        className="flex items-center gap-1"
                      >
                        <Plus className="h-3 w-3" />
                        Add Category
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-60 overflow-y-auto">
                      {metadataQuery.data.content_categories
                        .sort((a, b) => b.usageCount - a.usageCount)
                        .map((category) => (
                          <div key={category.value} className="flex items-center justify-between p-2 border rounded hover:bg-muted/50 transition-colors group">
                            <div className="flex items-center gap-2 flex-1">
                              <span className="text-sm font-medium capitalize">{category.value}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {category.usageCount} uses
                              </Badge>
                              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    toast.info("Content categories are enum values. Database schema changes required for renaming.");
                                  }}
                                  disabled={true}
                                >
                                  <Edit className="h-3 w-3 opacity-50" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                  onClick={() => {
                                    if (category.usageCount > 0) {
                                      const forceDelete = confirm(`"${category.value}" is used in ${category.usageCount} content pieces. Delete anyway? This may cause issues.`);
                                      if (forceDelete) {
                                        deleteContentCategoryMutation.mutate({ value: category.value, force: true });
                                      }
                                    } else {
                                      const confirmDelete = confirm(`Delete content category "${category.value}"?`);
                                      if (confirmDelete) {
                                        deleteContentCategoryMutation.mutate({ value: category.value });
                                      }
                                    }
                                  }}
                                  disabled={deleteContentCategoryMutation.isPending}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </Card>
                )}
              </div>
            )}

            {/* Metadata Statistics */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Metadata Statistics</h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-500">
                    {metadataQuery.data?.content_tags?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Tags</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-500">
                    {metadataQuery.data?.content_account?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Accounts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-500">
                    {metadataQuery.data?.content_types?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Types</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    {metadataQuery.data?.twitter_content_type?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Twitter Types</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-500">
                    {metadataQuery.data?.content_categories?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Categories</div>
                </div>
              </div>
            </Card>

            {/* Metadata Management Actions */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Advanced Management</h3>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground mb-4">
                  Use these tools to perform bulk operations on your metadata. All changes will update content immediately.
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => {
                      // Show bulk rename preview
                      if (!metadataQuery.data) return;
                      
                      const allValues = [
                        ...(metadataQuery.data.content_tags || []),
                        ...(metadataQuery.data.content_account || []),
                      ];
                      
                      const totalUsage = allValues.reduce((sum, item) => sum + item.usageCount, 0);
                      toast.info(`Total ${allValues.length} values across ${totalUsage} content pieces. Individual rename available per item.`);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                    Rename Analysis
                  </Button>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => {
                      // Show merge candidates
                      if (!metadataQuery.data?.content_account) return;
                      
                      // Find potential duplicates (case-insensitive similar names)
                      const accounts = metadataQuery.data.content_account;
                      const duplicateCandidates = accounts.filter((acc, index) => 
                        accounts.some((other, otherIndex) => 
                          index !== otherIndex && 
                          acc.value.toLowerCase().includes(other.value.toLowerCase()) ||
                          other.value.toLowerCase().includes(acc.value.toLowerCase())
                        )
                      );
                      
                      if (duplicateCandidates.length > 0) {
                        toast.info(`Found ${duplicateCandidates.length} potential duplicate accounts: ${duplicateCandidates.map(d => d.value).join(', ')}`);
                      } else {
                        toast.info("No obvious duplicate accounts found");
                      }
                    }}
                  >
                    <Database className="h-4 w-4" />
                    Find Duplicates
                  </Button>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => {
                      // Export metadata summary
                      if (!metadataQuery.data) return;
                      
                      const summary = {
                        exported_at: new Date().toISOString(),
                        content_tags: metadataQuery.data.content_tags || [],
                        content_accounts: metadataQuery.data.content_account || [],
                        content_types: metadataQuery.data.content_types || [],
                        twitter_content_types: metadataQuery.data.twitter_content_type || [],
                        content_categories: metadataQuery.data.content_categories || [],
                      };
                      
                      const blob = new Blob([JSON.stringify(summary, null, 2)], { type: 'application/json' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `metadata-export-${new Date().toISOString().split('T')[0]}.json`;
                      a.click();
                      URL.revokeObjectURL(url);
                      
                      toast.success("Metadata exported successfully!");
                    }}
                  >
                    <Database className="h-4 w-4" />
                    Export Metadata
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

      </div>
    </div>
  );
}

export default function AdminPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center"><Loader /></div>}>
      <AdminPageContent />
    </Suspense>
  );
}