import React from 'react';

interface IBCLogoProps {
  className?: string;
  width?: number;
  height?: number;
  preserveAspectRatio?: string;
}

export function IBCLogo({ 
  className = '', 
  width = 120, 
  height = 55,
  preserveAspectRatio = "xMidYMid meet"
}: IBCLogoProps) {
  return (
    <svg
      className={className}
      width={width}
      height={height}
      viewBox="0 0 600.04932 272.76532"
      preserveAspectRatio={preserveAspectRatio}
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <clipPath clipPathUnits="userSpaceOnUse" id="clipPath2">
          <path
            d="M 0,204.574 H 450.037 V 0 H 0 Z"
            transform="translate(-232.0213,-92.842205)"
          />
        </clipPath>
      </defs>
      <g>
        <path
          d="m 0,0 h -13.819 c -1.345,0 -2.436,1.09 -2.436,2.436 v 14.001 c 0,1.345 1.091,2.436 2.436,2.436 H 0 c 1.345,0 2.436,-1.091 2.436,-2.436 V 2.436 C 2.436,1.09 1.345,0 0,0 m -18.69,18.873 h -33.065 c -1.345,0 -2.435,1.09 -2.435,2.435 v 14.011 c 0,1.345 1.09,2.436 2.435,2.436 h 33.065 c 1.345,0 2.435,-1.091 2.435,-2.436 V 21.308 c 0,-1.345 -1.09,-2.435 -2.435,-2.435 m 2.435,-21.309 v -14.187 c 0,-1.345 -1.09,-2.436 -2.435,-2.436 h -33.07 c -1.345,0 -2.435,1.091 -2.435,2.436 v 14.187 c 0,1.346 1.09,2.436 2.435,2.436 h 33.07 c 1.345,0 2.435,-1.09 2.435,-2.436 m 96.623,21.309 h 14.013 c 1.345,0 2.435,1.09 2.435,2.435 v 14.011 c 0,1.345 -1.09,2.436 -2.435,2.436 H 80.368 c -1.345,0 -2.435,1.09 -2.435,2.435 v 14.012 c 0,1.346 -1.091,2.436 -2.436,2.436 H 40.226 c -0.019,0 0.02,0 0,0 L 22.027,38.468 C 21.569,38.011 21.312,37.391 21.312,36.745 V 21.308 c 0,-1.345 -1.091,-2.435 -2.436,-2.435 H 4.871 c -1.345,0 -2.435,1.09 -2.435,2.435 v 15.445 c 0,0.642 -0.254,1.258 -0.705,1.714 L -15.54,55.916 c -0.457,0.462 -1.081,0.722 -1.731,0.722 h -34.484 c -1.345,0 -2.435,-1.09 -2.435,-2.436 V 40.19 c 0,-1.345 -1.091,-2.435 -2.436,-2.435 h -14.011 c -1.345,0 -2.435,1.09 -2.435,2.435 v 14.012 c 0,1.346 -1.091,2.436 -2.436,2.436 h -14.004 c -1.345,0 -2.435,-1.09 -2.435,-2.436 V 40.19 c 0,-1.345 -1.091,-2.435 -2.436,-2.435 h -14.004 c -1.345,0 -2.435,-1.091 -2.435,-2.436 v -70.632 c 0,-1.345 1.09,-2.435 2.435,-2.435 h 14.004 c 1.345,0 2.436,1.09 2.436,2.435 v 70.632 c 0,1.345 1.09,2.436 2.435,2.436 h 14.004 c 1.345,0 2.436,-1.091 2.436,-2.436 v -70.632 c 0,-1.345 1.09,-2.435 2.435,-2.435 h 53.374 c 0.645,0 1.265,0.256 1.722,0.713 l 17.263,17.263 c 0.457,0.457 0.714,1.076 0.714,1.722 V -2.436 C 2.436,-1.09 3.526,0 4.871,0 h 14.005 c 1.345,0 2.436,-1.09 2.436,-2.436 v -15.605 c 0,-0.651 0.26,-1.275 0.723,-1.733 l 17.48,-17.271 c 0.456,-0.451 1.071,-0.703 1.711,-0.703 h 53.155 c 1.345,0 2.435,1.09 2.435,2.435 v 13.819 c 0,1.345 -1.09,2.435 -2.435,2.435 H 42.662 c -1.345,0 -2.436,1.091 -2.436,2.436 V -2.436 C 40.226,-1.09 39.136,0 37.791,0 H 23.747 c -1.345,0 -2.435,1.09 -2.435,2.436 v 14.001 c 0,1.345 1.09,2.436 2.435,2.436 h 14.044 c 1.345,0 2.435,1.09 2.435,2.435 v 14.011 c 0,1.345 1.091,2.436 2.436,2.436 h 32.835 c 1.345,0 2.436,-1.091 2.436,-2.436 V 21.308 c 0,-1.345 1.09,-2.435 2.435,-2.435"
          fill="currentColor"
          fillOpacity="1"
          fillRule="nonzero"
          stroke="none"
          transform="matrix(1.3333333,0,0,-1.3333333,309.36173,148.97573)"
          clipPath="url(#clipPath2)"
        />
      </g>
    </svg>
  );
}