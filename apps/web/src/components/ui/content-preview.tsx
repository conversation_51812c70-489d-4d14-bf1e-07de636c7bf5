import * as React from "react"
import { ExternalLink, Calendar, User } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "./button"
import { Badge } from "./badge"

interface ContentPreviewProps {
  title?: string
  description?: string
  account?: string
  platform?: "twitter" | "linkedin" | "youtube" | "other"
  createdDate?: string
  link?: string
  tags?: string[]
  className?: string
  variant?: "default" | "compact"
}

const platformColors = {
  twitter: "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300",
  linkedin: "bg-blue-600 text-white",
  youtube: "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300",
  other: "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
}

const platformLabels = {
  twitter: "Twitter",
  linkedin: "LinkedIn", 
  youtube: "YouTube",
  other: "Web"
}

export function ContentPreview({
  title,
  description,
  account,
  platform = "other",
  createdDate,
  link,
  tags = [],
  className,
  variant = "default"
}: ContentPreviewProps) {
  const handleViewContent = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (link) {
      window.open(link, "_blank", "noopener,noreferrer")
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return ""
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return "1 day ago"
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
    return date.toLocaleDateString()
  }

  if (variant === "compact") {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex items-start justify-between gap-2">
          <h3 className="font-medium text-gray-900 dark:text-white line-clamp-2 text-sm">
            {title || `Content from @${account}`}
          </h3>
          <Badge variant="outline" className={cn("text-xs flex-shrink-0", platformColors[platform])}>
            {platformLabels[platform]}
          </Badge>
        </div>
        
        {description && (
          <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
            {description.length > 100 ? `${description.substring(0, 100)}...` : description}
          </p>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-gray-500">
            {account && (
              <div className="flex items-center gap-1">
                <User className="h-3 w-3" />
                <span>@{account}</span>
              </div>
            )}
            {createdDate && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{formatDate(createdDate)}</span>
              </div>
            )}
          </div>
          
          {link && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleViewContent}
              className="h-6 px-2 text-xs hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-950"
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Header with title and platform badge */}
      <div className="flex items-start justify-between gap-3">
        <h3 className="font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1">
          {title || `Content from @${account}`}
        </h3>
        <Badge variant="outline" className={cn("flex-shrink-0", platformColors[platform])}>
          {platformLabels[platform]}
        </Badge>
      </div>

      {/* Description preview */}
      {description && (
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
          {description}
        </p>
      )}

      {/* Metadata */}
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center gap-3 text-gray-500">
          {account && (
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span>@{account}</span>
            </div>
          )}
          {createdDate && (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(createdDate)}</span>
            </div>
          )}
        </div>
      </div>

      {/* Tags */}
      {tags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs px-2 py-0.5">
              {tag}
            </Badge>
          ))}
          {tags.length > 3 && (
            <span className="text-xs text-gray-500 px-2 py-0.5">
              +{tags.length - 3} more
            </span>
          )}
        </div>
      )}

      {/* Action button */}
      {link && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleViewContent}
          className="w-full text-xs hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 dark:hover:bg-blue-950 dark:hover:text-blue-400 transition-colors"
        >
          <ExternalLink className="h-3 w-3 mr-2" />
          View on {platformLabels[platform]}
        </Button>
      )}
    </div>
  )
}