import * as React from "react"
import { Heart, MessageCircle, Repeat2, Eye, TrendingUp } from "lucide-react"
import { cn } from "@/lib/utils"

interface EngagementMetricsProps {
  views?: number
  listeners?: number
  impressions?: number
  followIncrease?: number
  className?: string
  variant?: "default" | "compact" | "detailed"
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

export function EngagementMetrics({
  views = 0,
  listeners = 0,
  impressions = 0,
  followIncrease = 0,
  className,
  variant = "default"
}: EngagementMetricsProps) {
  const metrics = [
    { value: views, icon: Eye, label: "views", color: "text-blue-500" },
    { value: listeners, icon: MessageCircle, label: "listeners", color: "text-purple-500" },
    { value: impressions, icon: TrendingUp, label: "impressions", color: "text-green-500" },
    { value: followIncrease, icon: Heart, label: "follow increase", color: "text-orange-500" }
  ].filter(metric => metric.value > 0)

  if (metrics.length === 0) return null

  if (variant === "compact") {
    return (
      <div className={cn("grid grid-cols-2 sm:grid-cols-4 gap-2 text-center", className)}>
        {views > 0 && (
          <div className="space-y-1">
            <div className="text-xs text-blue-600 font-medium">Views</div>
            <div className="text-xs font-bold">{formatNumber(views)}</div>
          </div>
        )}
        {listeners > 0 && (
          <div className="space-y-1">
            <div className="text-xs text-purple-600 font-medium">Listeners</div>
            <div className="text-xs font-bold">{formatNumber(listeners)}</div>
          </div>
        )}
        {impressions > 0 && (
          <div className="space-y-1">
            <div className="text-xs text-green-600 font-medium">Impressions</div>
            <div className="text-xs font-bold">{formatNumber(impressions)}</div>
          </div>
        )}
        {followIncrease > 0 && (
          <div className="space-y-1">
            <div className="text-xs text-orange-600 font-medium">Follow Increase</div>
            <div className="text-xs font-bold">{formatNumber(followIncrease)}</div>
          </div>
        )}
      </div>
    )
  }

  if (variant === "detailed") {
    return (
      <div className={cn("grid grid-cols-2 sm:grid-cols-4 gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg", className)}>
        {metrics.map(({ value, icon: Icon, label, color }) => (
          <div key={label} className="flex flex-col items-center text-center">
            <div className={cn("flex items-center gap-1 mb-1", color)}>
              <Icon className="h-3 w-3" />
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                {label}
              </span>
            </div>
            <span className="text-sm font-semibold text-gray-900 dark:text-white">
              {formatNumber(value)}
            </span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={cn("flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400", className)}>
      {metrics.map(({ value, icon: Icon, label, color }) => (
        <div key={label} className="flex items-center gap-1">
          <Icon className={cn("h-3 w-3", color)} />
          <span className="text-xs font-medium">{formatNumber(value)}</span>
        </div>
      ))}
    </div>
  )
}