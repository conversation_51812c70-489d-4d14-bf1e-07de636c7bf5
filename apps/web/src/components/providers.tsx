"use client";

import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { queryClient, trpc, trpcClient } from "@/utils/trpc";
import { Toaster } from "./ui/sonner";
import { AuthProvider } from "./auth/auth-provider";


export default function Providers({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          {children}
          <ReactQueryDevtools />
          <Toaster richColors />
        </AuthProvider>
      </QueryClientProvider>
    </trpc.Provider>
  );
}
