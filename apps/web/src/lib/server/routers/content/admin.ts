import {
  adminProcedure,
  router,
} from "../../trpc";
import { supabase, createServerSupabaseClient, type ContentPiece } from "../../supabase";
import { z } from "zod";

export const adminContentRouter = router({
  // Create new content entry (admin only)
  createContent: adminProcedure
    .input(z.object({
      content_link: z.string().url(),
      content_tags: z.array(z.string()).default([]),
      host: z.string().transform(val => val.toLowerCase()),
      content_account: z.array(z.string()).min(1),
      content_created_date: z.string(),
      content_types: z.array(z.string()).default([]),
      twitter_content_type: z.string().nullable().optional(),
      content_views: z.number().default(0),
      content_listeners: z.number().default(0),
      twitter_impressions: z.number().default(0),
      content_follow_increase: z.number().default(0),
      content_title: z.string().nullable().optional(),
      content_description: z.string().nullable().optional(),
      content_categories: z.array(z.string()).default([]),
    }))
    .mutation(async ({ input, ctx }) => {
      console.log("🔍 [CREATE CONTENT] Starting content creation for user:", ctx.user.email);
      console.log("🔍 [CREATE CONTENT] Input data:", input);

      // Use authenticated Supabase client to respect RLS policies
      const supabase = await createServerSupabaseClient();

      const { data, error } = await supabase
        .from('content_pieces')
        .insert([input])
        .select()
        .single();

      if (error) {
        console.error("❌ [CREATE CONTENT] Database error:", error);
        throw new Error(`Failed to create content: ${error.message}`);
      }

      console.log("✅ [CREATE CONTENT] Content created successfully:", data.id);
      return data as ContentPiece;
    }),

  // Update existing content entry (admin only)
  updateContent: adminProcedure
    .input(z.object({
      id: z.number(),
      content_uuid: z.string().optional(),
      content_link: z.string().url().optional(),
      content_tags: z.array(z.string()).optional(),
      host: z.string().transform(val => val.toLowerCase()).optional(),
      content_account: z.array(z.string()).min(1).optional(),
      content_created_date: z.string().optional(),
      content_types: z.array(z.string()).optional(),
      twitter_content_type: z.string().nullable().optional(),
      content_views: z.number().optional(),
      content_listeners: z.number().optional(),
      twitter_impressions: z.number().optional(),
      content_follow_increase: z.number().optional(),
      content_title: z.string().nullable().optional(),
      content_description: z.string().nullable().optional(),
      content_categories: z.array(z.string()).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { id, ...updates } = input;

      console.log("🔍 [UPDATE CONTENT] Starting content update for user:", ctx.user.email, "content ID:", id);

      // Use authenticated Supabase client to respect RLS policies
      const supabase = await createServerSupabaseClient();

      // First check if the content exists
      const { data: existingContent, error: checkError } = await supabase
        .from('content_pieces')
        .select('id, host')
        .eq('id', id)
        .single();

      if (checkError) {
        console.error("❌ [UPDATE CONTENT] Content not found:", checkError);
        throw new Error(`Content not found: ${checkError.message}`);
      }

      // If updating host, check for conflicts with other content pieces
      if (updates.host && updates.host !== existingContent.host) {
        const { data: conflictCheck, error: conflictError } = await supabase
          .from('content_pieces')
          .select('id')
          .eq('host', updates.host)
          .neq('id', id)
          .maybeSingle();

        if (conflictError) {
          console.error("❌ [UPDATE CONTENT] Error checking host uniqueness:", conflictError);
          throw new Error(`Error checking host uniqueness: ${conflictError.message}`);
        }

        if (conflictCheck) {
          throw new Error(`Host '${updates.host}' is already taken by another content piece`);
        }
      }

      const { data, error } = await supabase
        .from('content_pieces')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error("❌ [UPDATE CONTENT] Database error:", error);
        if (error.code === '23505' && error.message.includes('idx_content_pieces_host_unique')) {
          throw new Error(`Host '${updates.host}' is already taken. Twitter handles must be unique.`);
        }
        throw new Error(`Failed to update content: ${error.message}`);
      }

      console.log("✅ [UPDATE CONTENT] Content updated successfully:", data.id);
      return data as ContentPiece;
    }),

  // Delete content entry (admin only)
  deleteContent: adminProcedure
    .input(z.object({
      id: z.number(),
    }))
    .mutation(async ({ input, ctx }) => {
      console.log("🔍 [DELETE CONTENT] Starting content deletion for user:", ctx.user.email, "content ID:", input.id);

      // Use authenticated Supabase client to respect RLS policies
      const supabase = await createServerSupabaseClient();

      const { error } = await supabase
        .from('content_pieces')
        .delete()
        .eq('id', input.id);

      if (error) {
        console.error("❌ [DELETE CONTENT] Database error:", error);
        throw new Error(`Failed to delete content: ${error.message}`);
      }

      console.log("✅ [DELETE CONTENT] Content deleted successfully:", input.id);
      return { success: true };
    }),

  // Get single content entry by ID (admin only)
  getContentById: adminProcedure
    .input(z.object({
      id: z.number(),
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .eq('id', input.id)
        .single();

      if (error) {
        throw new Error(`Failed to fetch content: ${error.message}`);
      }

      return data as ContentPiece;
    }),

  // Bulk create content entries (admin only)
  bulkCreateContent: adminProcedure
    .input(z.object({
      entries: z.array(z.object({
        content_link: z.string().url(),
        content_tags: z.array(z.string()).default([]),
        content_account: z.string(),
        content_created_date: z.string(),
        content_types: z.array(z.string()).default([]),
        twitter_content_type: z.string().nullable().optional(),
        twitter_impressions: z.number().default(0),
        twitter_likes: z.number().default(0),
        twitter_retweets: z.number().default(0),
        content_title: z.string().nullable().optional(),
        content_description: z.string().nullable().optional(),
        content_categories: z.array(z.string()).default([]),
      }))
    }))
    .mutation(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .insert(input.entries)
        .select();

      if (error) {
        throw new Error(`Bulk create failed: ${error.message}`);
      }

      return {
        success: true,
        created: data?.length || 0,
        entries: data as ContentPiece[]
      };
    }),
});