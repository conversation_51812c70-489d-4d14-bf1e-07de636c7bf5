import {
  adminProcedure,
  router,
} from "../../trpc";
import { supabase } from "../../supabase";
import { z } from "zod";

export const metadataRouter = router({
  // Get all filter metadata with usage counts
  getFilterMetadata: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']).optional()
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase.rpc('get_filter_metadata', {
        filter_type: input.type || null
      });

      if (error) {
        throw new Error(`Failed to fetch filter metadata: ${error.message}`);
      }

      // Group the results by type
      const grouped = (data as Array<{type: string, value: string, usage_count: number}>).reduce((acc, item) => {
        if (!acc[item.type]) {
          acc[item.type] = [];
        }
        acc[item.type].push({
          value: item.value,
          usageCount: item.usage_count
        });
        return acc;
      }, {} as Record<string, Array<{value: string, usageCount: number}>>);

      return grouped;
    }),

  // Rename a filter value across all content
  renameFilterValue: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      oldValue: z.string(),
      newValue: z.string()
    }))
    .mutation(async ({ input }) => {
      const { type, oldValue, newValue } = input;
      
      let updateQuery;
      switch (type) {
        case 'content_account':
          updateQuery = supabase
            .from('content_pieces')
            .update({ content_account: newValue })
            .eq('content_account', oldValue);
          break;
        case 'twitter_content_type':
          // Validate that newValue is a valid twitter_content_type enum value
          const validTwitterContentTypes = ['space', 'interview', 'tweet', 'thread', 'retweet'] as const;
          type TwitterContentType = typeof validTwitterContentTypes[number];
          
          if (!validTwitterContentTypes.includes(newValue as TwitterContentType)) {
            throw new Error(`Invalid twitter_content_type: ${newValue}. Must be one of: ${validTwitterContentTypes.join(', ')}`);
          }
          
          updateQuery = supabase
            .from('content_pieces')
            .update({ twitter_content_type: newValue as TwitterContentType })
            .eq('twitter_content_type', oldValue);
          break;
        case 'content_tags':
          // For array fields, we need to use PostgreSQL array functions
          const { data: tagData, error: tagError } = await supabase.rpc('rename_array_value', {
            table_name: 'content_pieces',
            column_name: 'content_tags',
            old_value: oldValue,
            new_value: newValue
          });
          if (tagError) throw new Error(`Failed to rename content tag: ${tagError.message}`);
          return { success: true, updatedCount: tagData };
        case 'content_types':
          const { data: typeData, error: typeError } = await supabase.rpc('rename_array_value', {
            table_name: 'content_pieces',
            column_name: 'content_types',
            old_value: oldValue,
            new_value: newValue
          });
          if (typeError) throw new Error(`Failed to rename content type: ${typeError.message}`);
          return { success: true, updatedCount: typeData };
        case 'content_categories':
          const { data: catData, error: catError } = await supabase.rpc('rename_array_value', {
            table_name: 'content_pieces',
            column_name: 'content_categories',
            old_value: oldValue,
            new_value: newValue
          });
          if (catError) throw new Error(`Failed to rename content category: ${catError.message}`);
          return { success: true, updatedCount: catData };
      }

      const { data, error } = await updateQuery.select('id');
      if (error) {
        throw new Error(`Failed to rename ${type}: ${error.message}`);
      }

      return { success: true, updatedCount: data?.length || 0 };
    }),

  // Merge multiple filter values into one
  mergeFilterValues: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      sourceValues: z.array(z.string()),
      targetValue: z.string()
    }))
    .mutation(async ({ input }) => {
      const { type, sourceValues, targetValue } = input;
      let totalUpdated = 0;

      for (const sourceValue of sourceValues) {
        if (sourceValue === targetValue) continue; // Skip if already the target value

        let updateQuery;
        switch (type) {
          case 'content_account':
            updateQuery = supabase
              .from('content_pieces')
              .update({ content_account: targetValue })
              .eq('content_account', sourceValue);
            break;
          case 'twitter_content_type':
            updateQuery = supabase
              .from('content_pieces')
              .update({ twitter_content_type: targetValue as any })
              .eq('twitter_content_type', sourceValue);
            break;
          case 'content_tags':
            const { data: tagData, error: tagError } = await supabase.rpc('rename_array_value', {
              table_name: 'content_pieces',
              column_name: 'content_tags',
              old_value: sourceValue,
              new_value: targetValue
            });
            if (tagError) throw new Error(`Failed to merge content tag: ${tagError.message}`);
            totalUpdated += tagData || 0;
            continue;
          case 'content_types':
            const { data: typeData, error: typeError } = await supabase.rpc('rename_array_value', {
              table_name: 'content_pieces',
              column_name: 'content_types',
              old_value: sourceValue,
              new_value: targetValue
            });
            if (typeError) throw new Error(`Failed to merge content type: ${typeError.message}`);
            totalUpdated += typeData || 0;
            continue;
          case 'content_categories':
            const { data: catData, error: catError } = await supabase.rpc('rename_array_value', {
              table_name: 'content_pieces',
              column_name: 'content_categories',
              old_value: sourceValue,
              new_value: targetValue
            });
            if (catError) throw new Error(`Failed to merge content category: ${catError.message}`);
            totalUpdated += catData || 0;
            continue;
        }

        const { data, error } = await updateQuery.select('id');
        if (error) {
          throw new Error(`Failed to merge ${type}: ${error.message}`);
        }
        totalUpdated += data?.length || 0;
      }

      return { success: true, updatedCount: totalUpdated };
    }),

  // Delete unused filter values (only if usage count is 0)
  deleteFilterValue: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { type, value, force } = input;

      // First check usage count
      const { data: usage, error: usageError } = await supabase.rpc('get_value_usage_count', {
        filter_type: type,
        filter_value: value
      });

      if (usageError) {
        throw new Error(`Failed to check usage count: ${usageError.message}`);
      }

      if (usage > 0 && !force) {
        throw new Error(`Cannot delete ${type} "${value}" as it is used in ${usage} content pieces. Use force=true to delete anyway.`);
      }

      // For array types, we can't really "delete" the value from unused records as arrays don't have unused values
      // For scalar types like content_account, we would need to set them to null or another value
      if (type === 'content_account') {
        // We can't really delete account names that aren't used since they're required fields
        // This operation would typically be a "cleanup" rather than delete
        return { success: true, message: `Value "${value}" marked for cleanup (${usage} usages would be affected)` };
      }

      return { success: true, message: `Value "${value}" processed (${usage} usages)` };
    }),

  // Add new content type to the content_type enum
  addContentType: adminProcedure
    .input(z.object({
      value: z.string().min(1).max(50),
    }))
    .mutation(async ({ input }) => {
      const { value } = input;
      
      // Add the new value to the content_type enum
      const { error } = await supabase.rpc('add_enum_value', {
        enum_name: 'content_type',
        new_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to add content type: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Content type "${value}" added successfully` 
      };
    }),

  // Add new content category to the content_category enum  
  addContentCategory: adminProcedure
    .input(z.object({
      value: z.string().min(1).max(50),
    }))
    .mutation(async ({ input }) => {
      const { value } = input;
      
      // Add the new value to the content_category enum
      const { error } = await supabase.rpc('add_enum_value', {
        enum_name: 'content_category',
        new_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to add content category: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Content category "${value}" added successfully` 
      };
    }),

  // Add new Twitter content type to the twitter_content_type enum
  addTwitterContentType: adminProcedure
    .input(z.object({
      value: z.string().min(1).max(50),
    }))
    .mutation(async ({ input }) => {
      const { value } = input;
      
      // Add the new value to the twitter_content_type enum
      const { error } = await supabase.rpc('add_enum_value', {
        enum_name: 'twitter_content_type',
        new_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to add Twitter content type: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Twitter content type "${value}" added successfully` 
      };
    }),

  // Delete content type from enum (only if not in use)
  deleteContentType: adminProcedure
    .input(z.object({
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { value, force } = input;
      
      // Check if the type is in use
      const { data: usage, error: usageError } = await supabase
        .from('content_pieces')
        .select('id')
        .contains('content_types', [value]);

      if (usageError) {
        throw new Error(`Failed to check usage: ${usageError.message}`);
      }

      if (usage.length > 0 && !force) {
        throw new Error(`Cannot delete content type "${value}" as it is used in ${usage.length} content pieces. Use force to delete anyway.`);
      }

      // Remove the value from the enum
      const { error } = await supabase.rpc('remove_enum_value', {
        enum_name: 'content_type',
        old_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to delete content type: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Content type "${value}" deleted successfully` 
      };
    }),

  // Delete content category from enum (only if not in use)
  deleteContentCategory: adminProcedure
    .input(z.object({
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { value, force } = input;
      
      // Check if the category is in use
      const { data: usage, error: usageError } = await supabase
        .from('content_pieces')
        .select('id')
        .contains('content_categories', [value]);

      if (usageError) {
        throw new Error(`Failed to check usage: ${usageError.message}`);
      }

      if (usage.length > 0 && !force) {
        throw new Error(`Cannot delete content category "${value}" as it is used in ${usage.length} content pieces. Use force to delete anyway.`);
      }

      // Remove the value from the enum
      const { error } = await supabase.rpc('remove_enum_value', {
        enum_name: 'content_category',
        old_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to delete content category: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Content category "${value}" deleted successfully` 
      };
    }),

  // Delete Twitter content type from enum (only if not in use)
  deleteTwitterContentType: adminProcedure
    .input(z.object({
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { value, force } = input;
      
      // Check if the Twitter content type is in use
      const { data: usage, error: usageError } = await supabase
        .from('content_pieces')
        .select('id')
        .eq('twitter_content_type', value);

      if (usageError) {
        throw new Error(`Failed to check usage: ${usageError.message}`);
      }

      if (usage.length > 0 && !force) {
        throw new Error(`Cannot delete Twitter content type "${value}" as it is used in ${usage.length} content pieces. Use force to delete anyway.`);
      }

      // Remove the value from the enum
      const { error } = await supabase.rpc('remove_enum_value', {
        enum_name: 'twitter_content_type',
        old_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to delete Twitter content type: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Twitter content type "${value}" deleted successfully` 
      };
    }),

  // Remove a tag from all content pieces
  removeContentTag: adminProcedure
    .input(z.object({
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { value, force } = input;
      
      // Check if the tag is in use
      const { data: usage, error: usageError } = await supabase
        .from('content_pieces')
        .select('id')
        .contains('content_tags', [value]);

      if (usageError) {
        throw new Error(`Failed to check usage: ${usageError.message}`);
      }

      if (usage.length > 0 && !force) {
        throw new Error(`Cannot remove tag "${value}" as it is used in ${usage.length} content pieces. Use force to remove anyway.`);
      }

      // Remove the tag from all content pieces that have it
      const { data, error } = await supabase.rpc('remove_array_value', {
        table_name: 'content_pieces',
        column_name: 'content_tags',
        value_to_remove: value
      });

      if (error) {
        throw new Error(`Failed to remove tag: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Tag "${value}" removed from ${data || 0} content pieces` 
      };
    }),
});